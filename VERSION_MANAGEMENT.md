# PTSR模型版本管理系统

## 概述

本系统支持PTSR模型的版本管理，允许在保留原模型功能的基础上进行迭代改进。

## 版本说明

### 可用版本

- **original**: 原始PTSR模型，基于论文实现
- **v1**: 改进版本1 (待定义)
- **v2**: 改进版本2 (待定义)  
- **v3**: 改进版本3 (待定义)
- **v4**: 改进版本4 (待定义)
- **v5**: 改进版本5 (待定义)

## 使用方法

### 1. 直接使用main.py

```bash
# 训练原模型 (50轮)
python3 main.py --data_name mooc --epochs 50 --batch_size 256 --msg "original_model_v0" --model_version original --do_test --do_eval

# 训练改进版本v1
python3 main.py --data_name mooc --epochs 50 --batch_size 256 --msg "improved_v1" --model_version v1 --do_test --do_eval --version_desc "第一版改进"
```

### 2. 使用版本管理脚本

```bash
# 查看所有版本信息
python3 run_version.py --action info --version all

# 查看特定版本信息
python3 run_version.py --action info --version original

# 训练原模型
python3 run_version.py --version original --action train --epochs 50 --msg "baseline_training"

# 训练改进版本
python3 run_version.py --version v1 --action train --epochs 50 --msg "v1_training" --version_desc "添加了新的注意力机制"
```

## 文件结构

```
src/
├── main.py                 # 主训练脚本 (已修改支持版本管理)
├── model_factory.py        # 模型版本管理工厂
├── run_version.py          # 版本管理运行脚本
├── models.py              # 模型定义
├── trainers.py            # 训练器
└── ...
```

## 版本管理特性

### 1. 自动命名
- 原模型: `0628162647-mooc-original_model_v0.pt`
- 改进版本: `0628162647-mooc-improved_v1_v1.pt`

### 2. 版本隔离
- 每个版本的模型文件独立保存
- 配置参数可以版本特定
- 训练日志分别记录

### 3. 向后兼容
- 原模型功能完全保留
- 可以随时切换回原版本
- 支持版本间对比

## 添加新版本

### 1. 创建新模型类 (如需要)

```python
# 在models.py中添加
class PTSRModelV1(SASRecModel):
    def __init__(self):
        super().__init__()
        # 添加新的组件
        self.new_component = ...
    
    def forward(self, ...):
        # 实现改进的前向传播
        ...
```

### 2. 更新版本配置

```python
# 在model_factory.py中更新
VERSION_INFO = {
    "v1": {
        "name": "PTSR v1 - Enhanced Attention",
        "description": "添加了多头注意力机制和残差连接",
        "model_class": PTSRModelV1,
        "config_updates": {
            "num_attention_heads": 4,  # 示例配置更新
            "hidden_dropout_prob": 0.3
        }
    },
    ...
}
```

## 当前训练状态

🚀 **正在进行**: 原模型50轮训练
- 命令: `python3 main.py --data_name mooc --do_test --do_eval --msg "original_model_v0" --epochs 50 --batch_size 256`
- 状态: 运行中...
- 预计完成时间: 约30-60分钟

## 最佳实践

1. **保持原模型不变**: 始终保留original版本作为基线
2. **渐进式改进**: 每个版本只做少量改动，便于对比
3. **详细记录**: 使用`--version_desc`记录每个版本的改动
4. **性能对比**: 定期对比不同版本的性能指标
5. **代码备份**: 重要改动前备份代码

## 性能监控

系统会自动记录每个版本的:
- 训练损失曲线
- 验证集性能 (HIT@5, HIT@10, HIT@20, NDCG@5, NDCG@10, NDCG@20)
- 测试集性能
- 训练时间和资源使用

## 故障排除

如果遇到版本相关问题:
1. 检查`model_factory.py`中的版本配置
2. 确认模型类正确导入
3. 验证参数配置是否兼容
4. 查看训练日志中的错误信息
