# -*- coding: utf-8 -*-
#
# Copyright (c) 2022 salesforce.com, inc.
# All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause


import random

import numpy as np
import torch
from torch.utils.data import DataLoader, Dataset, RandomSampler, SequentialSampler

from cprint import pprint_color
from graph import TargetSubseqs
from param import args


class SRDataset(Dataset):
    def __init__(
        self,
        user_seq: list[list[int]],
        data_type: str = "train",
        subseq_id_map: dict = None,  # 传入映射避免全局变量依赖
    ) -> None:
        """torch.utils.dataDataset

        Args:
            user_seq (list[list[int]]): subseq list in the training phase and original sequence in the validation and testing phase. *Not including User_ID*.
            data_type (str, optional): dataset type. Defaults to "train". Choice: {"train", "valid", "test"}.
        """

        self.user_seq = user_seq
        self.data_type = data_type
        self.max_len: int = args.max_seq_length

        # 存储subseq_id_map以避免多进程问题
        self.subseq_id_map = subseq_id_map if subseq_id_map is not None else getattr(args, 'subseq_id_map', {})

        # create target item sets
        target_item_subseq = TargetSubseqs(args.subseqs_path, args.target_subseqs_path, args.subseqs_target_path)
        self.train_tag: dict[int, list[list[int]]] = target_item_subseq._load_target_subseqs_dict(
            args.target_subseqs_path, mode="train"
        )
        self.get_pad_user_seq()

    def __getitem__(self, index: int):
        """Get the data sample for the RecWithContrastiveLearningDataset.

        Example:

        ```
        [0, 1, 2, 3, 4, 5, 6]

        train:
        input_id [0, 1, 2, 3]
        target_pos [1, 2, 3, 4]
        target_pos_ sampled from the target item set
        answer [4]

        valid:
        input_id [0, 1, 2, 3, 4]
        target_pos [1, 2, 3, 4, 5]
        answer [5]

        test:
        input_id [0, 1, 2, 3, 4, 5]
        target_pos [1, 2, 3, 4, 5, 6]
        answer [6]
        ```

        Args:
            index (int): _description_

        Returns:
            tuple(Tensor):
        """
        user_id = index
        # * new loader_type: 1. use global pad sequence 2. drop target_pos sample 3. remove test noise interactions
        pad_user_seq = self.pad_user_seq_array[index]

        # * 获取 subseqs_id 做 extend task 的查表
        if self.data_type in ["train", "graph"]:  # subseq_id_map 只统计了训练集
            subseqs_id = self.subseq_id_map[self.pad_origin_map[self.pad_user_seq[index]][:-3]]
        if self.data_type == "train":
            input_ids = pad_user_seq[:-3]
            target_pos = pad_user_seq[1:-2]
            return (
                subseqs_id,  # 返回 subseqs_id 用于 extend task 通过 subseqs id 获取其子序列列表
                torch.from_numpy(input_ids),
                torch.from_numpy(target_pos),
            )
        if self.data_type == "graph":
            input_ids = pad_user_seq[:-3]
            return (torch.tensor(subseqs_id), torch.from_numpy(input_ids))
        elif self.data_type == "valid":
            input_ids = pad_user_seq[1:-2]
            answer = [pad_user_seq[-2]]
        else:
            input_ids = pad_user_seq[2:-1]
            answer = [pad_user_seq[-1]]
        return (
            torch.tensor(user_id),
            torch.from_numpy(input_ids),
            torch.tensor(answer),
        )
        raise ValueError(f"Invalid loader_type mode: {args.loader_mode}")

    def __len__(self):
        """consider n_view of a single sequence as one sample"""
        return len(self.user_seq)

    def get_pad_user_seq(self):
        """Prepare the padding in advance, so there's no need to do it again during each __getitem__()  of the Dataloader."""
        max_len = self.max_len + 3
        padded_user_seq = np.zeros((len(self.user_seq), max_len), dtype=int)

        for i, seq in enumerate(self.user_seq):
            padded_user_seq[i, -min(len(seq), max_len) :] = seq[-max_len:]

        self.pad_user_seq = tuple(map(tuple, padded_user_seq))
        self.pad_user_seq_array = np.array(self.pad_user_seq)

        user_seq = tuple(map(tuple, self.user_seq))
        self.origin_pad_map = dict(zip(user_seq, self.pad_user_seq))
        self.pad_origin_map = dict(zip(self.pad_user_seq, user_seq))

        # * 为了 Valid/Test 时做已 PAD 到原始的映射做 extend task 的查表
        if self.data_type == "valid":
            valid_user_seq = tuple([tuple(x[:-2]) for x in self.user_seq])
            valid_pad_user_seq = tuple([tuple(x[1:-2]) for x in self.pad_user_seq])
            self.valid_origin_pad_map = dict(zip(valid_pad_user_seq, valid_user_seq))

        if self.data_type == "test":
            test_user_seq = tuple([tuple(x[:-1]) for x in self.user_seq])
            test_pad_user_seq = tuple([tuple(x[2:-1]) for x in self.pad_user_seq])
            self.test_origin_pad_map = dict(zip(test_pad_user_seq, test_user_seq))


def build_dataloader(user_seq, loader_type, num_workers=None):
    sampler = RandomSampler if loader_type == "train" else SequentialSampler
    pprint_color(f">>> Building {loader_type} Dataloader")

    # 传递subseq_id_map以支持多进程
    subseq_id_map = getattr(args, 'subseq_id_map', {})
    dataset = SRDataset(user_seq, data_type=loader_type, subseq_id_map=subseq_id_map)

    # 如果没有指定num_workers，根据设备类型自动选择
    if num_workers is None:
        if hasattr(args, 'device') and args.device.type == 'mps':
            num_workers = 0  # MPS设备使用单进程更稳定
        else:
            num_workers = 2  # 其他设备可以尝试多进程

    return DataLoader(
        dataset,
        sampler=sampler(dataset),
        batch_size=args.batch_size,
        num_workers=num_workers,
        pin_memory=False if hasattr(args, 'device') and args.device.type == 'mps' else True,
        # persistent_workers=True if num_workers > 0 else False
    )
