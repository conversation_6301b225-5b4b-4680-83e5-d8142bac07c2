# -*- coding: utf-8 -*-
#
# Copyright (c) 2022 salesforce.com, inc.
# All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

from __future__ import annotations

import math
import os.path as osp
import time
import warnings
from ast import literal_eval
from typing import Optional, Union

import numpy as np
import torch
from torch import Tensor, nn
from torch.optim import AdamW
from torch.utils.data.dataloader import DataLoader
from tqdm import TqdmExperimentalWarning
from tqdm.rich import tqdm

from cprint import pprint_color
from graph import Graph
from metric import ndcg_k, recall_at_k
from models import GCN, SASRecModel
from param import args
from utils import EarlyStopping

warnings.filterwarnings("ignore", category=TqdmExperimentalWarning)


def do_train(trainer, valid_rating_matrix, test_rating_matrix):
    pprint_color(">>> Train PTSR Start")
    early_stopping = EarlyStopping(args.checkpoint_path, args.latest_path, patience=50)
    # trainer.load("/home/<USER>/proj/paper2/PTSR/ckpt/**********-Beauty-回归性原理_写完改动看看能否还原.pt")
    for epoch in range(args.epochs):
        args.rating_matrix = valid_rating_matrix
        trainer.train(epoch)
        # * evaluate on NDCG@20
        if args.do_eval and epoch >= args.min_test_epoch:
            scores, _ = trainer.valid(epoch)
            early_stopping(np.array(scores[-1:]), trainer.model)
            if early_stopping.early_stop:
                pprint_color(">>> Early stopping")
                break

        # * test on while training
        if args.do_test and epoch >= args.min_test_epoch:
            args.rating_matrix = test_rating_matrix
            _, _ = trainer.test(epoch, full_sort=True)


def do_eval(trainer, test_rating_matrix):
    pprint_color(">>> Test PTSR Start")
    pprint_color(f'>>> Load model from "{args.latest_path}" for test')
    args.rating_matrix = test_rating_matrix
    trainer.load(args.latest_path)
    _, _ = trainer.test(0, full_sort=True)


class Trainer:

    def __init__(
        self,
        model: Union[SASRecModel],
        train_dataloader: Optional[DataLoader],
        graph_dataloader: Optional[DataLoader],
        eval_dataloader: Optional[DataLoader],
        test_dataloader: DataLoader,
    ) -> None:
        pprint_color(">>> Initialize Trainer")

        # 使用全局设备配置
        self.device = getattr(args, 'device', torch.device('cpu'))
        torch.set_float32_matmul_precision(args.precision)

        self.model = torch.compile(model) if args.compile else model
        self.gcn = GCN()
        if args.cuda_condition:
            self.model.to(self.device)
            self.gcn.to(self.device)
        self.graph = Graph(args.graph_path)

        self.train_dataloader, self.graph_dataloader, self.eval_dataloader, self.test_dataloader = (
            train_dataloader,
            graph_dataloader,
            eval_dataloader,
            test_dataloader,
        )

        self.optim_adam = AdamW(self.model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
        self.scheduler = self.get_scheduler(self.optim_adam)

        self.best_scores = {
            "valid": {
                "Epoch": 0,
                "HIT@5": 0.0,
                "HIT@10": 0.0,
                "HIT@20": 0.0,
                "NDCG@5": 0.0,
                "NDCG@10": 0.0,
                "NDCG@20": 0.0,
            },
            "test": {
                "Epoch": 0,
                "HIT@5": 0.0,
                "HIT@10": 0.0,
                "HIT@20": 0.0,
                "NDCG@5": 0.0,
                "NDCG@10": 0.0,
                "NDCG@20": 0.0,
            },
        }

        # pprint_color(f">>> Total Parameters: {sum(p.nelement() for p in self.model.parameters())}")

    @staticmethod
    def get_result_log(post_fix):
        log_message = ""
        for key, value in post_fix.items():
            if isinstance(value, float):
                log_message += f" | {key}: {value:.4f}"
            else:
                log_message += f"{key}: [{value:03}]"
        return log_message

    def get_full_sort_score(
        self, epoch: int, answers: np.ndarray, pred_list: np.ndarray, mode
    ) -> tuple[list[float], str]:
        """
        Calculate the full sort score for a given epoch.

        Args:
            epoch (int): The epoch number.
            answers (np.ndarray): The ground truth answers. SHAPE: [user_num, 1]
            pred_list (np.ndarray): The predicted list of items. SHAPE: [user_num, 20]

        Returns:
            list: A list containing the recall and NDCG scores at different values of k.
            str: A string representation of the scores.

        """
        recall, ndcg = [], []
        for k in [5, 10, 20]:
            recall.append(recall_at_k(answers, pred_list, k))
            ndcg.append(ndcg_k(answers, pred_list, k))
        post_fix = {
            "Epoch": epoch,
            "HIT@5": recall[0],
            "HIT@10": recall[1],
            "HIT@20": recall[2],
            "NDCG@5": ndcg[0],
            "NDCG@10": ndcg[1],
            "NDCG@20": ndcg[2],
        }

        for key, value in post_fix.items():
            if key != "Epoch":
                args.tb.add_scalar(f"{mode}/{key}", value, epoch, new_style=True)

        args.logger.warning(self.get_result_log(post_fix))

        self.get_best_score(post_fix, mode)
        return [recall[0], ndcg[0], recall[1], ndcg[1], recall[2], ndcg[2]], str(post_fix)

    def get_best_score(self, scores, mode):
        improv = {
            "HIT@5": 0,
            "HIT@10": 0,
            "HIT@20": 0,
            "NDCG@5": 0,
            "NDCG@10": 0,
            "NDCG@20": 0,
        }
        self.best_scores[mode]["Epoch"] = scores["Epoch"]
        for key, value in scores.items():
            if key != "Epoch":
                improv[key] = value / (self.best_scores[mode][key] + 1e-12)
                self.best_scores[mode][key] = max(self.best_scores[mode][key], value)
                args.tb.add_scalar(
                    f"best_{mode}/best_{key}",
                    self.best_scores[mode][key],
                    self.best_scores[mode]["Epoch"],
                    new_style=True,
                )

        if mode == "test":
            args.logger.critical(self.get_result_log(self.best_scores[mode]))
            # transfer improv to %
            improv = {k: round((v - 1) * 100, 2) for k, v in improv.items()}
            improv_message = ""
            for key, value in improv.items():
                if isinstance(value, float):
                    improv_message += f" | {key}: {value:.2f}%"
            args.logger.critical(f"v.s. BEST   {improv_message}\n")
        else:
            args.logger.error(self.get_result_log(self.best_scores[mode]))

    def save(self, file_name: str):
        """Save the model to the file_name"""
        torch.save(self.model.cpu().state_dict(), file_name)
        self.model.to(self.device)

    def load(self, file_name: str):
        """Load the model from the file_name"""
        self.model.load_state_dict(torch.load(file_name))

    @staticmethod
    def get_scheduler(optimizer):
        if args.scheduler == "step":
            scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=args.step_size, gamma=args.gamma)
        elif args.scheduler == "cosine":
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.T_max, eta_min=args.min_lr)
        elif args.scheduler == "plateau":
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, mode="min", factor=args.factor, patience=args.patience, verbose=True
            )
        elif args.scheduler == "multistep":
            scheduler = torch.optim.lr_scheduler.MultiStepLR(
                optimizer, milestones=literal_eval(args.milestones), gamma=args.gamma
            )
            pprint_color(f">>> scheduler: {args.scheduler}, milestones: {args.milestones}, gamma: {args.gamma}")
        elif args.scheduler == "warmup+cosine":
            warm_up_with_cosine_lr = lambda epoch: (
                epoch / args.warm_up_epochs
                if epoch <= args.warm_up_epochs
                else 0.5 * (math.cos((epoch - args.warm_up_epochs) / (args.epochs - args.warm_up_epochs) * math.pi) + 1)
            )
            scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda=warm_up_with_cosine_lr)
        elif args.scheduler == "warmup+multistep":
            warm_up_with_multistep_lr = lambda epoch: (
                epoch / args.warm_up_epochs
                if epoch <= args.warm_up_epochs
                else args.gamma ** len([m for m in literal_eval(args.milestones) if m <= epoch])
            )
            scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda=warm_up_with_multistep_lr)
        else:
            raise ValueError("Invalid scheduler")
        return scheduler

    def train(self, epoch) -> None:
        assert self.train_dataloader is not None
        args.mode = "train"
        self.train_epoch(epoch, self.train_dataloader)

    def valid(self, epoch) -> tuple[list[float], str]:
        assert self.eval_dataloader is not None
        args.mode = "valid"
        return self.full_test_epoch(epoch, self.eval_dataloader, "valid")

    def test(self, epoch, full_sort=False) -> tuple[list[float], str]:
        args.mode = "test"
        if full_sort:
            return self.full_test_epoch(epoch, self.test_dataloader, "test")
        return self.sample_test_epoch(epoch, self.test_dataloader)

    def log(self, epoch, post_fix) -> None:
        if (epoch + 1) % args.log_freq == 0:
            loss_message = ""
            for key, value in post_fix.items():
                if "loss" in key:
                    args.tb.add_scalar(f"train/{key}", value, epoch, new_style=True)
                if isinstance(value, float):
                    loss_message += f" | {key}: {value}"
                else:
                    loss_message += f"{key}: [{value:03}]"

            loss_message += f" | Message: {args.save_name}"
            args.logger.info(loss_message)


# MARK: PTSR
class PTSRTrainer(Trainer):

    def __init__(
        self,
        model: SASRecModel,
        train_dataloader: DataLoader | None,
        graph_dataloader: DataLoader | None,
        eval_dataloader: DataLoader | None,
        test_dataloader: DataLoader,
    ) -> None:
        super().__init__(model, train_dataloader, graph_dataloader, eval_dataloader, test_dataloader)

        # * prepare padding subseq for subseq embedding update
        self.all_subseq, self.pad_mask, self.num_non_pad = self.get_all_pad_subseq(self.graph_dataloader)
        self.id_prefix_map, self.id_prefixid_map = self.get_train_prefix_sub(self.graph_dataloader)
        self.loss_func = nn.CrossEntropyLoss()

        # * self.id_prefixid_map: key 是输入的 prefix, value 是该 prefix 的子序列的 ID 列表
        # * 训练/预测时, 通过这个 map 应该能直接获得已经 padding 的子序列 ID 列表
        # * 所以要对这个 ID 列表做 Padding
        def get_id_pad(dic, length=50):
            return {
                # k: np.pad(v, (max(0, length - len(v)), 0), mode="constant", constant_values=0)[-length:]
                k: np.pad(v, (0, max(0, length - len(v))), mode="constant", constant_values=0)[-length:]
                for k, v in dic.items()
            }

        self.id_pad_prefix_map = get_id_pad(self.id_prefixid_map, 50)

        self.valid_prefix, self.test_prefix = self.get_test_prefix_sub(self.test_dataloader)

    # MARK: Train
    def train_epoch(self, epoch, train_dataloader):
        self.model.train()
        if epoch == 0:
            train_matrix = self.graph.edge_random_dropout(self.graph.train_matrix, args.dropout_rate)
            self.graph.torch_A = self.graph.get_torch_adj(train_matrix)

        rec_avg_loss = 0.0
        cl_avg_loss = 0.0
        avg_loss = 0.0
        batch_num = len(train_dataloader)
        args.tb.add_scalar("train/LR", self.optim_adam.param_groups[0]["lr"], epoch, new_style=True)

        # * two different update: 1. update subseq embeddings 2. gcn update
        # * update subseq embeddings: 1. every epoch(√) 2. every batch 3. no update
        if args.gcn_mode != "None":
            self.subseq_embed_update(epoch)
        # * update GCN (GCN Propogation): 1. every epoch 2. every batch(√) 3. no update
        if args.gcn_mode == "global":  # GCN forward every epoch
            _, self.model.all_item_emb = self.gcn(
                self.graph.torch_A, self.model.subseq_embeddings.weight, self.model.item_embeddings.weight
            )

        train_desc = f"{args.save_name} | Device: {args.gpu_id} | Rec Training Epoch {epoch}"

        for batch_i, (batch) in tqdm(
            enumerate(train_dataloader), total=batch_num, leave=False, desc=train_desc, dynamic_ncols=True
        ):
            # * rec_batch shape: key_name x batch_size x feature_dim
            batch = tuple(t.to(self.device) for t in batch)
            subseq_ids, input_ids, gt_ids = batch

            # * GCN forward propogation
            if args.gcn_mode in ["batch", "batch_gcn"] and args.mode == "train":  # GCN forward every batch
                self.model.all_subseq_emb, self.model.all_item_emb = self.gcn(
                    self.graph.torch_A, self.model.subseq_embeddings.weight, self.model.item_embeddings.weight
                )

            # * prediction task
            intent_output = self.model(input_ids)

            # * Extension Task
            def query_prefix(d, keys):
                arrays = [d[key.item()] for key in keys if key.item() in d]
                stacked_array = np.stack(arrays, axis=0)
                tensor = torch.from_numpy(stacked_array).to(self.device)
                return tensor

            if args.extend:
                prefix = query_prefix(self.id_pad_prefix_map, subseq_ids)
                # intent_output = torch.cat(
                #     (intent_output, self.model.forward_p(prefix, sp=intent_output[:, -1, :])), dim=2
                # )
                intent_output = intent_output + args.a * self.model.forward_p(prefix, sp=intent_output[:, -1, :])

            # * predict & loss
            logits = self.model.predict_full(intent_output[:, -1, :])
            rec_loss = self.loss_func(logits, gt_ids[:, -1])
            # * logits: [256, 50, 12103]
            # * gt_ids: [256, 50, ]
            # logits = self.model.predict_full(intent_output)
            # rec_loss = nn.CrossEntropyLoss()(logits.reshape(-1, logits.shape[-1]), gt_ids.reshape(-1))

            self.optim_adam.zero_grad()
            rec_loss.backward()
            self.optim_adam.step()

            rec_avg_loss += rec_loss.item()
            # cl_avg_loss += cl_loss.item()
            # avg_loss += loss.item()

        self.scheduler.step()
        # * print & write log for each epoch
        # * post_fix: print the average loss of the epoch
        post_fix = {
            "Epoch": epoch,
            "lr": round(self.optim_adam.param_groups[0]["lr"], 6),
            "rec_avg_loss": round(rec_avg_loss / batch_num, 4),
            "cl_avg_loss": round(cl_avg_loss / batch_num, 4),
            "avg_loss": round(avg_loss / batch_num, 4),
        }
        self.log(epoch, post_fix)
        # self.emb_vis(epoch)

    # MARK: TEST
    def full_test_epoch(self, epoch: int, dataloader: DataLoader, mode):
        with torch.no_grad():
            self.model.eval()
            # * gcn is fixed in the test phase. So it's unnecessary to call gcn() every batch.
            if args.gcn_mode != "None":
                self.model.all_subseq_emb, self.model.all_item_emb = self.gcn(
                    self.graph.torch_A, self.model.subseq_embeddings.weight, self.model.item_embeddings.weight
                )

            test_desc = f"{args.save_name} | Device: {args.gpu_id} | Test Epoch {epoch}"

            for batch_i, batch in tqdm(
                enumerate(dataloader),
                total=len(dataloader),
                leave=False,
                desc=test_desc,
                dynamic_ncols=True,
            ):
                batch = tuple(t.to(self.device) for t in batch)
                user_ids, input_ids, answers = batch

                # * SHAPE: [Batch_size, Seq_len, Hidden_size] -> [256, 50, 64]
                # * Use the last item output. SHAPE: [Batch_size, Hidden_size] -> [256, 64]
                recommend_output: Tensor = self.model(input_ids)  # [BxLxH]

                # * Extension Task
                if args.extend:
                    if mode == "valid":
                        original_seqs = [
                            dataloader.dataset.valid_origin_pad_map[tuple(input_id.tolist())] for input_id in input_ids
                        ]
                    elif mode == "test":
                        original_seqs = [
                            dataloader.dataset.test_origin_pad_map[tuple(input_id.tolist())] for input_id in input_ids
                        ]
                    prefix = self.valid_prefix if mode == "valid" else self.test_prefix
                    prefix = (
                        torch.stack([prefix[tuple_key] for tuple_key in original_seqs if tuple_key in prefix])
                        .long()
                        .squeeze(1)
                        .to(self.device)
                    )
                    # recommend_output = torch.cat(
                    #     (recommend_output, self.model.forward_p(prefix, sp=recommend_output[:, -1, :])), dim=2
                    # )
                    recommend_output = recommend_output + args.a * self.model.forward_p(
                        prefix, sp=recommend_output[:, -1, :]
                    )

                # * recommendation results. SHAPE: [Batch_size, Item_size]
                rating_pred = self.model.predict_full(recommend_output[:, -1, :])

                batch_pred_list = self.test_postprocess(rating_pred, user_ids)

                if batch_i == 0:
                    pred_list = batch_pred_list
                    answer_list = answers.cpu().data.numpy()
                else:
                    pred_list = np.append(pred_list, batch_pred_list, axis=0)
                    answer_list = np.append(answer_list, answers.cpu().data.numpy(), axis=0)

            return self.get_full_sort_score(epoch, answer_list, pred_list, mode)

    def test_postprocess(self, rating_pred, user_ids):
        rating_pred = rating_pred.cpu().data.numpy().copy()
        batch_user_index = user_ids.cpu().numpy()

        # * 将已经有评分的 item 的预测评分设置为 0, 防止推荐已经评分过的 item
        rating_pred[args.rating_matrix[batch_user_index].toarray() > 0] = 0
        # * argpartition T: O(n)  argsort O(nlogn) | reference: https://stackoverflow.com/a/23734295, https://stackoverflow.com/a/20104162
        # * Get the *index* of the largest 20 items, but its order is not sorted. SHAPE: [Batch_size, 20]
        ind: np.ndarray = np.argpartition(rating_pred, -20)[:, -20:]

        # * np.arange(len(rating_pred): [0, 1, 2, ..., Batch_size-1]. SHAPE: [Batch_size]
        # * np.arange(len(rating_pred))[:, None]: [[0], [1], [2], ..., [Batch_size-1]]. SHAPE: [Batch_size, 1]

        # * Get the *value* of the largest 20 items. SHAPE: [Batch_size, 20]
        arr_ind = rating_pred[np.arange(len(rating_pred))[:, None], ind]
        # * Sort the largest 20 items's value to get the index. SHAPE: [Batch_size, 20]
        # * ATTENTION: `arr_ind_argsort` is the index of the `ind`, not the index of the `rating_pred`
        arr_ind_argsort = np.argsort(arr_ind)[np.arange(len(rating_pred)), ::-1]
        # * Get the real Item ID of the largest 20 items. SHAPE: [Batch_size, 20]
        batch_pred_list = ind[np.arange(len(rating_pred))[:, None], arr_ind_argsort]
        return batch_pred_list

    # MARK: 全局PAD预处理:
    def get_all_pad_subseq(self, gcn_dataloader: DataLoader) -> tuple[Tensor, Tensor]:
        """collect all padding subsequence index and subsequence for updating subseq embeddings.

        Returns:
            tuple[Tensor, Tensor, Tensor]:
                - all_subseq_ids: subseq id for all_subseq.
                - all_subseq: padding subseq (index, not embedding), e.g., [[0,0,1,2,3], [0,1,2,3,4], ...]
                - pad_mask: valid subseq mask, e.g., [[False, False, True, True, True], [False, True, True, True, True], ...]
                - num_non_pad: number of valid item in each subseq, e.g., [[3], [4], ...]
        """
        all_pad_subseq_path = f"../data/{args.data_name}_all_pad_subseq.pth"
        if not osp.exists(all_pad_subseq_path):
            all_subseq_ids = []
            all_subseq = []
            for _, (rec_batch) in tqdm(
                enumerate(gcn_dataloader),
                total=len(gcn_dataloader),
                desc=f"{args.save_name} | Device: {args.gpu_id} | get_all_pad_subseq",
                leave=False,
                dynamic_ncols=True,
            ):
                subseq_id, subsequence = rec_batch
                all_subseq_ids.append(subseq_id)
                all_subseq.append(subsequence)
            all_subseq_ids = torch.cat(all_subseq_ids, dim=0)
            all_subseq = torch.cat(all_subseq, dim=0)

            # * remove duplicate subsequence
            tensor_np = all_subseq_ids.numpy()
            _, indices = np.unique(tensor_np, axis=0, return_index=True)
            sorted_indices = np.sort(indices)
            all_subseq_ids = all_subseq_ids[sorted_indices]
            all_subseq = all_subseq[sorted_indices]
            # * check if ID is always increasing
            # print(torch.all(torch.diff(all_subseq_ids) > 0))
            # id_padded_subseq_map = dict(zip(all_subseq_ids, all_subseq))
            torch.save(all_subseq, all_pad_subseq_path)
        else:
            for _, (rec_batch) in tqdm(
                enumerate(gcn_dataloader),
                total=len(gcn_dataloader),
                desc=f"{args.save_name} | Device: {args.gpu_id} | get_all_pad_subseq",
                leave=False,
                dynamic_ncols=True,
            ):
                continue
            all_subseq = torch.load(all_pad_subseq_path)
            all_subseq_ids = torch.arange(all_subseq.size(0))
        # * pad_mask & num_non_pad: 用于计算 subseq embedding 的平均值
        pad_mask = all_subseq > 0
        num_non_pad = pad_mask.sum(dim=1, keepdim=True)
        return all_subseq, pad_mask, num_non_pad

    # MARK: Subseq更新策略: Mean Item
    def subseq_embed_update(self, epoch):
        self.model.item_embeddings.cpu()
        self.model.subseq_embeddings.cpu()
        subseq_emb = self.model.item_embeddings(self.all_subseq)
        subseq_emb_avg: Tensor = torch.sum(subseq_emb * self.pad_mask.unsqueeze(-1), dim=1) / self.num_non_pad
        # * Three subseq embed update methods: 1. nn.Parameter 2. nn.Embedding 3. model.subseq_embeddings
        # self.model.subseq_embeddings = nn.Parameter(subseq_emb_avg)
        # self.model.subseq_embeddings = subseq_emb_avg

        # * accelerate convergence
        self.model.subseq_embeddings.weight.data = (
            subseq_emb_avg if epoch == 0 else (subseq_emb_avg + self.model.subseq_embeddings.weight.data) / 2
        )

        self.model.item_embeddings.to(self.device)
        self.model.subseq_embeddings.to(self.device)

    # MARK: Subeq在图中出现过的子序列
    def get_train_prefix_sub(self, gcn_dataloader):
        """给定一个 user_seq, 返回 dict: {subseq_id: sub_prefix}

        基本逻辑:
            1. 从 args.subseq_id_map 中获取所有 subseqs
            2. 然后对每一个 subseq 去循环找子串, 子串判断条件是"是否在 args.subseq_id_map" 的 key 中
            3. 如果在就将子串和子串ID都收集起来，形成两个字典
            4. 字典的 Key 是 subseq_id, value 是子串本身(即子串item列表,如[1,2,3])或者子串ID


        Returns:
            _type_: _description_
        """
        id_prefix_map = {}
        id_prefixid_map = {}

        def check_subsequences_in_dict(subseq):
            n = len(subseq)
            prefix = []
            prefix_id = []
            for i in range(n, 0, -1):  # 从n到1（包括1），步长为-1
                sub = tuple(subseq[i - 1 : n])  # 获取子序列并转换成元组，因为字典的键是不可变类型
                if sub in args.subseq_id_map:
                    prefix.append(sub)
                    prefix_id.append(args.subseq_id_map[sub])
            return prefix, prefix_id

        for subseq, subseq_id in args.subseq_id_map.items():
            id_prefix_map[subseq_id], id_prefixid_map[subseq_id] = check_subsequences_in_dict(subseq)

        return id_prefix_map, id_prefixid_map

    def get_test_prefix_sub(self, test_dataloader):
        user_seq = test_dataloader.dataset.user_seq
        valid_input = [sub[:-2] for sub in user_seq]
        test_input = [sub[:-1] for sub in user_seq]

        def get_subseq_prefix(subseq):
            n = len(subseq)
            prefix_id = []
            for i in range(n, 0, -1):  # 从n到1（包括1），步长为-1
                sub = tuple(subseq[i - 1 : n])  # 获取子序列并转换成元组，因为字典的键是不可变类型
                if sub in args.subseq_id_map:
                    prefix_id.append(args.subseq_id_map[sub])
            return prefix_id

        valid = {}
        test = {}

        def pad_or_trim_lists(lists, l):
            """
            对于给定的列表lists中的每个子列表，将其扩展成长度为l的形式。
            如果子列表长度小于l，则在前面补0；
            如果子列表长度大于l，则从后面开始裁剪至长度为l。

            :param lists: 包含子列表的列表
            :param l: 目标长度
            :return: 处理后的 numpy.ndarray
            """
            padded_arrays = []
            for sublist in lists:
                # 转换为 NumPy 数组
                arr = np.array(sublist)

                # 如果子列表长度大于l，进行裁剪
                if len(arr) > l:
                    padded_arr = arr[-l:]  # 保留最后l个元素
                else:
                    # 计算需要补充0的数量
                    padding_size = l - len(arr)
                    # 使用np.pad进行填充
                    # padded_arr = np.pad(arr, (padding_size, 0), "constant")
                    padded_arr = np.pad(arr, (0, padding_size), "constant")

                padded_arrays.append(padded_arr)

            # 将所有数组堆叠成一个二维数组
            result_array = np.stack(padded_arrays)
            result_tensor = torch.from_numpy(result_array)

            return result_tensor

        for seq in valid_input:
            prefix = get_subseq_prefix(seq)
            pad_prefix = pad_or_trim_lists([prefix], 50)
            valid[tuple(seq)] = pad_prefix

        for seq in test_input:
            prefix = get_subseq_prefix(seq)
            pad_prefix = pad_or_trim_lists([prefix], 50)
            test[tuple(seq)] = pad_prefix

        return valid, test

    # 无效函数
    def emb_vis(self, epoch):
        metadata = [f"Item_{i}" for i in range(args.item_size)]
        args.tb.add_embedding(
            self.model.item_embeddings.weight, metadata=metadata, tag="ItemEmbeddings", global_step=epoch
        )
        args.tb.add_embedding(self.model.all_item_emb, metadata=metadata, tag="ItemEmbeddings", global_step=epoch)
        metadata = [f"Subseq_{i}" for i in range(args.num_subseq_id)]
        args.tb.add_embedding(self.model.all_subseq_emb, metadata=metadata, tag="SubseqEmbeddings", global_step=epoch)
