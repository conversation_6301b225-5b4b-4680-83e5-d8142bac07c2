#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PTSR版本管理运行脚本
支持原模型和改进版本的训练和测试
"""

import argparse
import subprocess
import sys
from model_factory import ModelVersionManager


def main():
    parser = argparse.ArgumentParser(description="PTSR Model Version Manager")
    
    # 基本参数
    parser.add_argument("--version", type=str, default="original",
                       choices=["original", "v1", "v2", "v3", "v4", "v5"],
                       help="Model version to run")
    parser.add_argument("--action", type=str, default="train",
                       choices=["train", "test", "info"],
                       help="Action to perform")
    
    # 训练参数
    parser.add_argument("--epochs", type=int, default=50,
                       help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=256,
                       help="Batch size")
    parser.add_argument("--data_name", type=str, default="mooc",
                       help="Dataset name")
    parser.add_argument("--msg", type=str, required=True,
                       help="Message for this run")
    
    # 可选参数
    parser.add_argument("--do_test", action="store_true", default=True,
                       help="Perform testing")
    parser.add_argument("--do_eval", action="store_true", default=True,
                       help="Perform evaluation")
    parser.add_argument("--version_desc", type=str, default="",
                       help="Description of version changes")
    
    args = parser.parse_args()
    
    if args.action == "info":
        # 显示版本信息
        if args.version == "all":
            ModelVersionManager.print_version_info()
        else:
            ModelVersionManager.print_version_info(args.version)
        return
    
    # 构建命令
    cmd = [
        "python3", "main.py",
        "--data_name", args.data_name,
        "--epochs", str(args.epochs),
        "--batch_size", str(args.batch_size),
        "--msg", args.msg,
        "--model_version", args.version
    ]
    
    if args.do_test:
        cmd.append("--do_test")
    if args.do_eval:
        cmd.append("--do_eval")
    if args.version_desc:
        cmd.extend(["--version_desc", args.version_desc])
    
    # 显示运行信息
    print("=" * 60)
    print(f"Running PTSR {args.version.upper()}")
    print("=" * 60)
    
    version_info = ModelVersionManager.get_version_info(args.version)
    print(f"Model: {version_info['name']}")
    print(f"Description: {version_info['description']}")
    print(f"Action: {args.action}")
    print(f"Command: {' '.join(cmd)}")
    print("=" * 60)
    
    # 执行命令
    try:
        result = subprocess.run(cmd, check=True)
        print(f"\n✅ {args.action.capitalize()} completed successfully!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {args.action.capitalize()} failed with return code {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print(f"\n⚠️ {args.action.capitalize()} interrupted by user")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
