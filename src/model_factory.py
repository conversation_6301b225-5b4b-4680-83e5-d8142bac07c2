# -*- coding: utf-8 -*-
"""
PTSR模型版本管理工厂
支持原模型和多个改进版本的并存
"""

from models import SASRecModel
from param import args


class ModelVersionManager:
    """模型版本管理器"""
    
    VERSION_INFO = {
        "original": {
            "name": "PTSR Original",
            "description": "原始PTSR模型，基于论文实现",
            "model_class": SASRecModel,
            "config_updates": {}
        },
        "v1": {
            "name": "PTSR v1",
            "description": "改进版本1 - 待定义",
            "model_class": SASRecModel,  # 后续会创建新的模型类
            "config_updates": {}
        },
        "v2": {
            "name": "PTSR v2",
            "description": "改进版本2 - 待定义",
            "model_class": SASRecModel,
            "config_updates": {}
        },
        "v3": {
            "name": "PTSR v3",
            "description": "改进版本3 - 待定义",
            "model_class": SASRecModel,
            "config_updates": {}
        },
        "v4": {
            "name": "PTSR v4",
            "description": "改进版本4 - 待定义",
            "model_class": SASRecModel,
            "config_updates": {}
        },
        "v5": {
            "name": "PTSR v5",
            "description": "改进版本5 - 待定义",
            "model_class": SASRecModel,
            "config_updates": {}
        }
    }
    
    @classmethod
    def get_model(cls, version="original"):
        """获取指定版本的模型"""
        if version not in cls.VERSION_INFO:
            raise ValueError(f"Unknown model version: {version}. Available: {list(cls.VERSION_INFO.keys())}")
        
        version_info = cls.VERSION_INFO[version]
        model_class = version_info["model_class"]
        
        # 应用版本特定的配置更新
        config_updates = version_info["config_updates"]
        for key, value in config_updates.items():
            setattr(args, key, value)
        
        # 创建模型实例
        model = model_class()
        
        return model, version_info
    
    @classmethod
    def get_version_info(cls, version="original"):
        """获取版本信息"""
        if version not in cls.VERSION_INFO:
            raise ValueError(f"Unknown model version: {version}")
        return cls.VERSION_INFO[version]
    
    @classmethod
    def list_versions(cls):
        """列出所有可用版本"""
        return list(cls.VERSION_INFO.keys())
    
    @classmethod
    def print_version_info(cls, version=None):
        """打印版本信息"""
        if version:
            if version not in cls.VERSION_INFO:
                print(f"Unknown version: {version}")
                return
            versions_to_print = [version]
        else:
            versions_to_print = cls.VERSION_INFO.keys()
        
        print("=" * 60)
        print("PTSR Model Versions")
        print("=" * 60)
        
        for v in versions_to_print:
            info = cls.VERSION_INFO[v]
            print(f"Version: {v}")
            print(f"  Name: {info['name']}")
            print(f"  Description: {info['description']}")
            print(f"  Model Class: {info['model_class'].__name__}")
            if info['config_updates']:
                print(f"  Config Updates: {info['config_updates']}")
            print("-" * 40)


def create_model(version="original"):
    """创建指定版本的模型"""
    return ModelVersionManager.get_model(version)


def get_model_save_name(base_name, version="original"):
    """生成版本特定的模型保存名称"""
    if version == "original":
        return base_name
    else:
        return f"{base_name}_{version}"


# 版本兼容性检查
def check_version_compatibility():
    """检查版本兼容性"""
    current_version = getattr(args, 'model_version', 'original')
    
    if current_version not in ModelVersionManager.VERSION_INFO:
        raise ValueError(f"Unsupported model version: {current_version}")
    
    return True


if __name__ == "__main__":
    # 测试版本管理器
    ModelVersionManager.print_version_info()
